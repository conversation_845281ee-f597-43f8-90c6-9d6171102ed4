/**
 * HTMX Form Generator
 * Converts FormVisionResult schema to HTMX markup
 */

export type {
  FormButton,
  FormControls,
  FormField,
  FormMetadata,
  ExtractionResult as FormVisionResult,
} from './types/form-interfaces';

import {
  generateAlertSummary,
  generateCheckboxField,
  generateErrorMessages,
  generateFloatingLabelField,
  generateFormButton,
  generateFormCloseTag,
  generateFormOpenTag,
  generatePromptDisplay,
  generateRadioField,
  generateStandardField,
  generateTextareaField,
  getErrorMessages,
} from './components';
import { ClassificationResult } from './types';
import type { ExtractionResult, FormField } from './types/form-interfaces';

/**
 * Generates HTMX form markup from FormVisionResult
 */
export class HTMXFormGenerator {
  generateForm(formData: ExtractionResult, classificationResult: ClassificationResult): string {
    const { screenInfo, controls } = formData;
    const {
      screenInfo: { instruction, screenCode, alertsSummary, alerts, alertsCount },
    } = classificationResult;
    const formElements: string[] = [];

    formElements.push(generateFormOpenTag());

    const errorMessages = getErrorMessages(screenInfo);

    // Add alert summary if present and has alerts
    if (alertsSummary && alertsCount > 0) {
      formElements.push(generateAlertSummary(alertsSummary, alerts, alertsCount));
    }

    // we dont display the instruction if it matches with any of the field label present
    const displayInstruction = !controls.fields.some((field) => {
      return `${field.label.toLowerCase()}.` === instruction.toLowerCase();
    });

    // Add instruction prominently if present and not matching description or title
    if (instruction && errorMessages.length === 0) {
      formElements.push(generatePromptDisplay(instruction, screenCode, displayInstruction));
    }

    if (errorMessages.length > 0) {
      formElements.push(generateErrorMessages(errorMessages));
    }

    // Generate form fields
    controls.fields.forEach((field) => {
      if (field.isLikelyDropdown) {
        return;
      }
      const fieldMarkup = this.generateFormField(field);
      if (fieldMarkup) {
        formElements.push(fieldMarkup);
      }
    });
    controls.buttons.forEach((button) => {
      if (
        classificationResult.screenInfo.screenClass === 'passkey-screen' &&
        button.variant === 'primary'
      ) {
        return;
      }
      formElements.push(generateFormButton(button));
    });

    formElements.push(generateFormCloseTag());

    return formElements.join('\n');
  }

  /**
   * Generate form field based on field type
   */
  private generateFormField(field: FormField): string | null {
    switch (field.fieldControlType) {
      case 'text':
      case 'password':
      case 'number':
        return generateFloatingLabelField(field);
      case 'select':
      case 'dropdown':
        return null;
      case 'checkbox':
        return generateCheckboxField(field);
      case 'checkboxgroup':
        return generateCheckboxField(field);
      case 'radiogroup':
        return generateRadioField(field);
      case 'textarea':
        return generateTextareaField(field);
      default:
        return generateStandardField(field);
    }
  }
}

// Export singleton instance
export const htmxFormGenerator = new HTMXFormGenerator();
