/**
 * Alert summary component
 */

import type { Alert } from '../types/classification-interfaces';

/**
 * Generate alert summary display markup
 */
export function generateAlertSummary(
  alertsSummary: string,
  alerts: Alert[],
  alertsCount: number,
): string {
  if (!alertsSummary || alertsCount === 0) return '';

  const alertIcon = getAlertIcon('error');

  return `
    <div class="error p-4 mb-4 rounded-lg border-l-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <div class="w-6 h-6 rounded-full flex items-center justify-center">
            ${alertIcon}
          </div>
        </div>
        <div class="ml-3 flex-1">
          <div class="text-sm">
            ${alertsSummary}
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Determine the primary alert type based on severity
 */
function getPrimaryAlertType(alerts: Alert[]): Alert['alertType'] {
  if (alerts.some((alert) => alert.alertType === 'error')) return 'error';
  if (alerts.some((alert) => alert.alertType === 'security')) return 'security';
  if (alerts.some((alert) => alert.alertType === 'warning')) return 'warning';
  return 'notice';
}

/**
 * Get CSS classes for alert styling based on type
 */
function getAlertClass(type: Alert['alertType']): string {
  switch (type) {
    case 'error':
      return 'bg-red-50 border-red-400 text-red-800';
    case 'security':
      return 'bg-orange-50 border-orange-400 text-orange-800';
    case 'warning':
      return 'bg-yellow-50 border-yellow-400 text-yellow-800';
    case 'notice':
    default:
      return 'bg-blue-50 border-blue-400 text-blue-800';
  }
}

/**
 * Get icon SVG for alert type
 */
function getAlertIcon(type: Alert['alertType']): string {
  switch (type) {
    case 'error':
      return `
        <svg class="h-4 w-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
      `;
    case 'security':
      return `
        <svg class="h-4 w-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
        </svg>
      `;
    case 'warning':
      return `
        <svg class="h-4 w-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
      `;
    case 'notice':
    default:
      return `
        <svg class="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>
      `;
  }
}
