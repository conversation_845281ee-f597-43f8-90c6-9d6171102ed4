import { ErrorContext, ProcessedError, LogLevel } from './types';
import { createErrorDetail } from '../web/httpHelpers';
import { ErrorCode, SupportedLocales } from '../web/localizationMessages';
import { WorkflowStepName } from '../../workflow/types/WorkflowStepName';

export class ErrorRouter {
  static processError(errorContext: ErrorContext, locale: SupportedLocales = 'en'): ProcessedError {
    const shouldReplaceCard = this.shouldReplaceCard(errorContext);
    const userMessage = this.getUserMessage(errorContext, locale);

    return {
      userMessage,
      shouldReplaceCard,
      errorCode: errorContext.errorCode,
      logLevel: errorContext.logLevel,
    };
  }

  private static shouldReplaceCard(errorContext: ErrorContext): boolean {
    const criticalErrorCodes: ErrorCode[] = [
      'BROWSER_CONNECTION_FAILED',
      'SCRIPT_INJECTION_FAILED',
      'CDP_SESSION_FAILED',
      'BROWSER_CONTROLLER_INIT_FAILED',
      'SCREEN_CROPPER_INIT_FAILED',
      'CAPTCHA_DETECTOR_INIT_FAILED',
      'SESSION_EXPIRED',
    ];

    const criticalSources = ['browser_connection', 'script_injection'];

    return (
      criticalErrorCodes.includes(errorContext.errorCode) ||
      criticalSources.includes(errorContext.source) ||
      errorContext.logLevel === 'error'
    );
  }

  private static getUserMessage(errorContext: ErrorContext, locale: SupportedLocales): string {
    const errorDetail = createErrorDetail(errorContext.errorCode, locale);
    return errorDetail.message;
  }

  static classifyError(errorContext: ErrorContext): ErrorContext {
    // Reclassify CDP errors based on technical details
    if (errorContext.source === 'cdp') {
      const updatedContext = { ...errorContext };

      if (errorContext.technicalDetails?.cdpEvent === 'Runtime.exceptionThrown') {
        const exceptionDetails = errorContext.technicalDetails.exceptionDetails;

        if (this.isConnectionError(exceptionDetails)) {
          updatedContext.errorCode = 'BROWSER_CONNECTION_FAILED';
        } else if (this.isScriptInjectionError(exceptionDetails)) {
          updatedContext.errorCode = 'SCRIPT_INJECTION_FAILED';
        }
      }

      return updatedContext;
    } else if (errorContext.source === 'workflow') {
      const updatedContext = { ...errorContext };
      const stepName = errorContext.technicalDetails.stepName;
      const isFormActions = stepName === WorkflowStepName.EXECUTE_FORM_ACTIONS;
      const isTimeout = this.isWorkflowTimeoutError(errorContext.technicalDetails?.error);

      if (isFormActions && isTimeout) {
        console.log('Converting WORKFLOW_STEP_FAILED to SESSION_EXPIRED');
        updatedContext.errorCode = 'SESSION_EXPIRED';
      }
      return updatedContext;
    }

    return errorContext;
  }

  private static isConnectionError(exceptionDetails: any): boolean {
    if (!exceptionDetails?.exception?.description) return false;

    const description = exceptionDetails.exception.description.toLowerCase();
    const connectionKeywords = [
      'websocket',
      'connection',
      'network',
      'timeout',
      'refused',
      'disconnected',
    ];

    return connectionKeywords.some((keyword) => description.includes(keyword));
  }

  private static isScriptInjectionError(exceptionDetails: any): boolean {
    if (!exceptionDetails?.exception?.description) return false;

    const description = exceptionDetails.exception.description.toLowerCase();
    const scriptKeywords = [
      'inject',
      'browser controller',
      'screen-cropper',
      'captcha-detector',
      'tf detector',
      'captcha detector',
    ];

    return scriptKeywords.some((keyword) => description.includes(keyword));
  }

  private static isWorkflowTimeoutError(error: any): boolean {
    if (!error) {
      console.log('No error object provided to isWorkflowTimeoutError');
      return false;
    }

    const errorMessage = error.message || error.toString() || '';
    const timeoutKeywords = [
      'timed out',
      'timeout',
      'execution timed out',
      'step timed out',
      'workflow timed out',
    ];

    const isTimeout = timeoutKeywords.some((keyword) =>
      errorMessage.toLowerCase().includes(keyword.toLowerCase()),
    );
    return isTimeout;
  }
}
