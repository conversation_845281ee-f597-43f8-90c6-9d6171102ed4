/**
 * Test for alert summary component
 */

import { describe, it, expect } from 'vitest';
import { generateAlertSummary } from '../../src/form-generation/components/alert-summary';
import { Alert } from '../../src/form-generation/types/classification-interfaces';

describe('Alert Summary Component', () => {
  it('should return empty string when no alerts', () => {
    const result = generateAlertSummary('', [], 0);
    expect(result).toBe('');
  });

  it('should return empty string when alertsSummary is empty', () => {
    const alerts: Alert[] = [
      { alertType: 'error', alertText: 'Test error' }
    ];
    const result = generateAlertSummary('', alerts, 1);
    expect(result).toBe('');
  });

  it('should generate alert summary for single error', () => {
    const alerts: Alert[] = [
      { alertType: 'error', alertText: 'Invalid credentials' }
    ];
    const result = generateAlertSummary('Your login credentials are incorrect', alerts, 1);
    
    expect(result).toContain('1 Alert Detected');
    expect(result).toContain('Your login credentials are incorrect');
    expect(result).toContain('bg-red-50 border-red-400 text-red-800');
  });

  it('should generate alert summary for multiple alerts', () => {
    const alerts: Alert[] = [
      { alertType: 'error', alertText: 'Invalid credentials' },
      { alertType: 'warning', alertText: 'Account will be locked' }
    ];
    const result = generateAlertSummary('Multiple issues detected with your account', alerts, 2);
    
    expect(result).toContain('2 Alerts Detected');
    expect(result).toContain('Multiple issues detected with your account');
    expect(result).toContain('bg-red-50 border-red-400 text-red-800'); // Should use error styling (highest priority)
  });

  it('should prioritize error over other alert types', () => {
    const alerts: Alert[] = [
      { alertType: 'notice', alertText: 'Info message' },
      { alertType: 'warning', alertText: 'Warning message' },
      { alertType: 'error', alertText: 'Error message' }
    ];
    const result = generateAlertSummary('Multiple alerts present', alerts, 3);
    
    expect(result).toContain('bg-red-50 border-red-400 text-red-800'); // Error styling
  });

  it('should prioritize security over warning and notice', () => {
    const alerts: Alert[] = [
      { alertType: 'notice', alertText: 'Info message' },
      { alertType: 'warning', alertText: 'Warning message' },
      { alertType: 'security', alertText: 'Security message' }
    ];
    const result = generateAlertSummary('Security and other alerts', alerts, 3);
    
    expect(result).toContain('bg-orange-50 border-orange-400 text-orange-800'); // Security styling
  });

  it('should prioritize warning over notice', () => {
    const alerts: Alert[] = [
      { alertType: 'notice', alertText: 'Info message' },
      { alertType: 'warning', alertText: 'Warning message' }
    ];
    const result = generateAlertSummary('Warning and notice alerts', alerts, 2);
    
    expect(result).toContain('bg-yellow-50 border-yellow-400 text-yellow-800'); // Warning styling
  });

  it('should use notice styling when only notice alerts', () => {
    const alerts: Alert[] = [
      { alertType: 'notice', alertText: 'Info message' }
    ];
    const result = generateAlertSummary('Information notice', alerts, 1);
    
    expect(result).toContain('bg-blue-50 border-blue-400 text-blue-800'); // Notice styling
  });
});
