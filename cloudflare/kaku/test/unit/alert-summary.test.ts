/**
 * Test for alert summary component
 */

import { describe, it, expect } from 'vitest';
import { generateAlertSummary } from '../../src/form-generation/components/alert-summary';

describe('Alert Summary Component', () => {
  it('should return empty string when alertsSummary is empty', () => {
    const result = generateAlertSummary('');
    expect(result).toBe('');
  });

  it('should generate alert summary with error styling', () => {
    const result = generateAlertSummary('Your login credentials are incorrect');

    expect(result).toContain('Your login credentials are incorrect');
    expect(result).toContain('bg-red-50 border-red-400 text-red-800');
    expect(result).toContain('text-red-600'); // Error icon color
  });

  it('should generate alert summary for longer messages', () => {
    const longMessage = 'Multiple issues detected with your account that need immediate attention';
    const result = generateAlertSummary(longMessage);

    expect(result).toContain(longMessage);
    expect(result).toContain('bg-red-50 border-red-400 text-red-800');
  });

  it('should include error icon in the alert', () => {
    const result = generateAlertSummary('Test alert message');

    expect(result).toContain('<svg');
    expect(result).toContain('text-red-600');
    expect(result).toContain('viewBox="0 0 20 20"');
  });
});
